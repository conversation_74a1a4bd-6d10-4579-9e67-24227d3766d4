#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSM匹配后数据图表生成脚本
PSM Post-Analysis Charts Generator

基于PSM_Matched_Final_Dataset.csv生成可视化图表
Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

# 颜色配置
COLORS = {
    'EYLEA': '#2E86AB',      # 蓝色
    'FARICIMAB': '#A23B72',  # 紫红色
}

def load_psm_data():
    """加载PSM匹配后的数据"""
    print("📂 加载PSM匹配后数据...")
    
    # 读取PSM匹配后的数据
    df = pd.read_csv('../../PSM_Matched_Final_Dataset.csv')
    
    print(f"✓ PSM数据加载完成: {len(df)}例患者")
    print(f"✓ 药物分组: {df['Drug'].value_counts().to_dict()}")
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    
    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'
        
        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    # 转换logMAR到ETDRS letters
    for col in bcva_cols:
        if col in df.columns:
            df[f'{col}_ETDRS'] = 85 - 50 * df[col]
    
    return df

def generate_bcva_four_panel_analysis(df):
    """生成BCVA四图分析"""
    print("📊 生成BCVA四图分析...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('PSM Matched Cohort: BCVA Analysis by MNV Subtype', fontsize=16, fontweight='bold')
    
    # 定义分析组合
    analyses = [
        ('Overall', None, axes[0, 0]),
        ('MNV Type 1', 1, axes[0, 1]),
        ('MNV Type 2', 2, axes[1, 0]),
        ('MNV Type 3', 3, axes[1, 1])
    ]
    
    timepoints = ['BL', 'Post-LP', 'Year 1']
    timepoint_labels = ['Baseline', 'Post-Loading', '12 Months']
    
    for title, mnv_type, ax in analyses:
        # 筛选数据
        if mnv_type is None:
            subset = df.copy()
        else:
            subset = df[df['MNV Type'] == mnv_type].copy()
        
        # 计算各时间点的ETDRS letters
        faricimab_means = []
        faricimab_stds = []
        eylea_means = []
        eylea_stds = []
        faricimab_ns = []
        eylea_ns = []
        
        for tp in timepoints:
            col = f'BCVA ({tp})_ETDRS'
            if col in subset.columns:
                faricimab_data = subset[subset['Drug'] == 'FARICIMAB'][col].dropna()
                eylea_data = subset[subset['Drug'] == 'EYLEA'][col].dropna()
                
                faricimab_means.append(faricimab_data.mean() if len(faricimab_data) > 0 else np.nan)
                faricimab_stds.append(faricimab_data.std() if len(faricimab_data) > 0 else np.nan)
                eylea_means.append(eylea_data.mean() if len(eylea_data) > 0 else np.nan)
                eylea_stds.append(eylea_data.std() if len(eylea_data) > 0 else np.nan)
                faricimab_ns.append(len(faricimab_data))
                eylea_ns.append(len(eylea_data))
            else:
                faricimab_means.append(np.nan)
                faricimab_stds.append(np.nan)
                eylea_means.append(np.nan)
                eylea_stds.append(np.nan)
                faricimab_ns.append(0)
                eylea_ns.append(0)
        
        # 绘制折线图
        x_pos = np.arange(len(timepoint_labels))
        
        # FARICIMAB线
        ax.errorbar(x_pos, faricimab_means, yerr=faricimab_stds, 
                   color=COLORS['FARICIMAB'], marker='o', linewidth=2, 
                   markersize=8, label='FARICIMAB', capsize=5)
        
        # EYLEA线
        ax.errorbar(x_pos, eylea_means, yerr=eylea_stds, 
                   color=COLORS['EYLEA'], marker='s', linewidth=2, 
                   markersize=8, label='EYLEA', capsize=5)
        
        # 添加样本量标注
        for i, (f_n, e_n) in enumerate(zip(faricimab_ns, eylea_ns)):
            if f_n > 0 or e_n > 0:
                ax.text(i, ax.get_ylim()[1] * 0.95, f'n={f_n}/{e_n}', 
                       ha='center', va='top', fontsize=10, 
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        ax.set_title(f'{title}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('BCVA (ETDRS Letters)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(timepoint_labels)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存文件
    output_path = '../charts/PSM_BCVA_Four_Panel_Analysis.pdf'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ BCVA四图分析已保存: {output_path}")

def generate_morphology_heatmap(df):
    """生成形态学热图"""
    print("📊 生成形态学热图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('PSM Matched Cohort: Morphological Features Heatmap', fontsize=16, fontweight='bold')
    
    # 形态学指标
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    timepoint_labels = ['Baseline', 'Post-Loading', '12 Months']
    
    # 为每个药物组创建热图
    drugs = ['EYLEA', 'FARICIMAB']
    
    for drug_idx, drug in enumerate(drugs):
        for metric_type in ['rate', 'count']:
            if metric_type == 'rate':
                ax = axes[drug_idx, 0]
                title_suffix = 'Positive Rate (%)'
            else:
                ax = axes[drug_idx, 1]
                title_suffix = 'Sample Size (n)'
            
            # 准备数据矩阵
            data_matrix = []
            
            for indicator in morphology_indicators:
                row_data = []
                for tp in timepoints:
                    col_name = f'{indicator} ({tp})'
                    if col_name in df.columns:
                        drug_data = df[df['Drug'] == drug][col_name].dropna()
                        if len(drug_data) > 0:
                            if metric_type == 'rate':
                                value = drug_data.sum() / len(drug_data) * 100
                            else:
                                value = len(drug_data)
                        else:
                            value = 0
                    else:
                        value = 0
                    row_data.append(value)
                data_matrix.append(row_data)
            
            # 创建热图
            if metric_type == 'rate':
                sns.heatmap(data_matrix, annot=True, fmt='.1f', cmap='RdYlBu_r',
                           xticklabels=timepoint_labels, yticklabels=morphology_indicators,
                           ax=ax, cbar_kws={'label': 'Positive Rate (%)'})
            else:
                sns.heatmap(data_matrix, annot=True, fmt='d', cmap='Blues',
                           xticklabels=timepoint_labels, yticklabels=morphology_indicators,
                           ax=ax, cbar_kws={'label': 'Sample Size'})
            
            ax.set_title(f'{drug} - {title_suffix}', fontsize=14, fontweight='bold')
            ax.set_xlabel('Timepoint', fontsize=12)
            if metric_type == 'rate':
                ax.set_ylabel('Morphological Feature', fontsize=12)
    
    plt.tight_layout()
    
    # 保存文件
    output_path = '../charts/PSM_Morphology_Heatmap.pdf'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ 形态学热图已保存: {output_path}")

def generate_morphology_analysis_by_subtype(df, mnv_type=None, title_suffix="Overall"):
    """生成特定MNV亚型的形态学分析"""
    print(f"📊 生成{title_suffix}形态学分析...")

    # 筛选数据
    if mnv_type is None:
        subset = df.copy()
    else:
        subset = df[df['MNV Type'] == mnv_type].copy()

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'PSM Matched Cohort: Morphological Analysis - {title_suffix}', fontsize=16, fontweight='bold')

    # 形态学指标
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    timepoint_labels = ['Baseline', 'Post-Loading', '12 Months']

    for idx, indicator in enumerate(morphology_indicators):
        ax = axes[idx // 2, idx % 2]

        # 计算各时间点的阳性率
        faricimab_rates = []
        eylea_rates = []
        faricimab_ns = []
        eylea_ns = []

        for tp in timepoints:
            col_name = f'{indicator} ({tp})'
            if col_name in subset.columns:
                faricimab_data = subset[subset['Drug'] == 'FARICIMAB'][col_name].dropna()
                eylea_data = subset[subset['Drug'] == 'EYLEA'][col_name].dropna()

                if len(faricimab_data) > 0:
                    faricimab_rate = faricimab_data.sum() / len(faricimab_data) * 100
                    faricimab_rates.append(faricimab_rate)
                    faricimab_ns.append(len(faricimab_data))
                else:
                    faricimab_rates.append(0)
                    faricimab_ns.append(0)

                if len(eylea_data) > 0:
                    eylea_rate = eylea_data.sum() / len(eylea_data) * 100
                    eylea_rates.append(eylea_rate)
                    eylea_ns.append(len(eylea_data))
                else:
                    eylea_rates.append(0)
                    eylea_ns.append(0)
            else:
                faricimab_rates.append(0)
                eylea_rates.append(0)
                faricimab_ns.append(0)
                eylea_ns.append(0)

        # 绘制柱状图
        x_pos = np.arange(len(timepoint_labels))
        width = 0.35

        bars1 = ax.bar(x_pos - width/2, faricimab_rates, width,
                      color=COLORS['FARICIMAB'], alpha=0.8, label='FARICIMAB')
        bars2 = ax.bar(x_pos + width/2, eylea_rates, width,
                      color=COLORS['EYLEA'], alpha=0.8, label='EYLEA')

        # 添加数值标签
        for i, (bar1, bar2, f_rate, e_rate) in enumerate(zip(bars1, bars2, faricimab_rates, eylea_rates)):
            ax.text(bar1.get_x() + bar1.get_width()/2, bar1.get_height() + 1,
                   f'{f_rate:.1f}%', ha='center', va='bottom', fontsize=10)
            ax.text(bar2.get_x() + bar2.get_width()/2, bar2.get_height() + 1,
                   f'{e_rate:.1f}%', ha='center', va='bottom', fontsize=10)

        # 添加折线图显示趋势
        ax2 = ax.twinx()
        ax2.plot(x_pos, faricimab_rates, color=COLORS['FARICIMAB'], marker='o', linewidth=2, markersize=6)
        ax2.plot(x_pos, eylea_rates, color=COLORS['EYLEA'], marker='s', linewidth=2, markersize=6)
        ax2.set_ylim(ax.get_ylim())
        ax2.set_yticks([])

        ax.set_title(f'{indicator}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('Positive Rate (%)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(timepoint_labels)
        ax.set_ylim(0, max(max(faricimab_rates), max(eylea_rates)) * 1.2 + 10)

        if idx == 0:
            ax.legend()

        ax.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存文件
    if mnv_type is None:
        filename = 'PSM_Morphology_Overall_Analysis.pdf'
    else:
        filename = f'PSM_Morphology_MNV_Type_{mnv_type}_Analysis.pdf'

    output_path = f'../charts/{filename}'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ {title_suffix}形态学分析已保存: {output_path}")

def main():
    """主函数"""
    print("🚀 开始生成PSM匹配后分析图表...")

    # 创建输出目录
    import os
    os.makedirs('../charts', exist_ok=True)

    # 加载数据
    df = load_psm_data()

    # 生成图表
    print("\n" + "="*50)
    generate_bcva_four_panel_analysis(df)

    print("\n" + "="*50)
    generate_morphology_heatmap(df)

    print("\n" + "="*50)
    generate_morphology_analysis_by_subtype(df, None, "Overall")

    print("\n" + "="*50)
    generate_morphology_analysis_by_subtype(df, 1, "MNV Type 1")

    print("\n" + "="*50)
    generate_morphology_analysis_by_subtype(df, 2, "MNV Type 2")

    print("\n" + "="*50)
    generate_morphology_analysis_by_subtype(df, 3, "MNV Type 3")

    print("\n✅ PSM匹配后图表生成完成!")
    print("📁 输出文件位置:")
    print("  - BCVA四图分析: ../charts/PSM_BCVA_Four_Panel_Analysis.pdf")
    print("  - 形态学热图: ../charts/PSM_Morphology_Heatmap.pdf")
    print("  - 整体形态学分析: ../charts/PSM_Morphology_Overall_Analysis.pdf")
    print("  - MNV Type 1分析: ../charts/PSM_Morphology_MNV_Type_1_Analysis.pdf")
    print("  - MNV Type 2分析: ../charts/PSM_Morphology_MNV_Type_2_Analysis.pdf")
    print("  - MNV Type 3分析: ../charts/PSM_Morphology_MNV_Type_3_Analysis.pdf")

if __name__ == "__main__":
    main()
