#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSM匹配后数据表格生成脚本
PSM Post-Analysis Tables Generator

基于PSM_Matched_Final_Dataset.csv生成四个标准表格
Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
import os
import re
from datetime import datetime
warnings.filterwarnings('ignore')

def load_psm_data():
    """加载PSM匹配后的数据"""
    print("📂 加载PSM匹配后数据...")
    
    # 读取PSM匹配后的数据
    df = pd.read_csv('../../PSM_Matched_Final_Dataset.csv')
    
    print(f"✓ PSM数据加载完成: {len(df)}例患者")
    print(f"✓ 药物分组: {df['Drug'].value_counts().to_dict()}")
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    
    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'
        
        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    return df

def convert_logmar_to_etdrs(logmar_values):
    """将logMAR转换为ETDRS letters"""
    return 85 - 50 * logmar_values

def format_pvalue(p):
    """格式化P值"""
    if p < 0.001:
        return "<0.001"
    elif p < 0.01:
        return f"{p:.3f}"
    else:
        return f"{p:.2f}"

def generate_table1_baseline_characteristics(df):
    """生成Table 1: 基线特征表"""
    print("\n📋 生成Table 1: 基线特征表...")
    
    # 分组数据
    faricimab = df[df['Drug'] == 'FARICIMAB']
    eylea = df[df['Drug'] == 'EYLEA']
    
    results = []
    
    # 样本量
    results.append({
        'Characteristic': 'Sample size, n',
        'FARICIMAB (n=44)': str(len(faricimab)),
        'EYLEA (n=44)': str(len(eylea)),
        'P-value': '-'
    })
    
    # 年龄
    if 'Age' in df.columns:
        age_f = faricimab['Age'].dropna()
        age_e = eylea['Age'].dropna()
        _, p_age = stats.ttest_ind(age_f, age_e)
        
        results.append({
            'Characteristic': 'Age, years (mean ± SD)',
            'FARICIMAB (n=44)': f"{age_f.mean():.1f} ± {age_f.std():.1f}",
            'EYLEA (n=44)': f"{age_e.mean():.1f} ± {age_e.std():.1f}",
            'P-value': format_pvalue(p_age)
        })
    
    # 性别
    if 'Female' in df.columns:
        female_f = faricimab['Female'].sum()
        female_e = eylea['Female'].sum()
        
        # 卡方检验
        contingency = [[female_f, len(faricimab) - female_f],
                      [female_e, len(eylea) - female_e]]
        _, p_sex = stats.chi2_contingency(contingency)[:2]
        
        results.append({
            'Characteristic': 'Female, n (%)',
            'FARICIMAB (n=44)': f"{female_f} ({female_f/len(faricimab)*100:.1f})",
            'EYLEA (n=44)': f"{female_e} ({female_e/len(eylea)*100:.1f})",
            'P-value': format_pvalue(p_sex)
        })
    
    # 研究眼别
    if 'Study Eye' in df.columns:
        od_f = (faricimab['Study Eye'].str.upper() == 'OD').sum()
        od_e = (eylea['Study Eye'].str.upper() == 'OD').sum()
        
        contingency = [[od_f, len(faricimab) - od_f],
                      [od_e, len(eylea) - od_e]]
        _, p_eye = stats.chi2_contingency(contingency)[:2]
        
        results.append({
            'Characteristic': 'Study eye OD, n (%)',
            'FARICIMAB (n=44)': f"{od_f} ({od_f/len(faricimab)*100:.1f})",
            'EYLEA (n=44)': f"{od_e} ({od_e/len(eylea)*100:.1f})",
            'P-value': format_pvalue(p_eye)
        })
    
    # MNV分型
    for mnv_type in [1, 2, 3]:
        col_name = f'MNV_Type_{mnv_type}'
        if col_name in df.columns:
            mnv_f = faricimab[col_name].sum()
            mnv_e = eylea[col_name].sum()
            
            contingency = [[mnv_f, len(faricimab) - mnv_f],
                          [mnv_e, len(eylea) - mnv_e]]
            _, p_mnv = stats.chi2_contingency(contingency)[:2]
            
            results.append({
                'Characteristic': f'MNV Type {mnv_type}, n (%)',
                'FARICIMAB (n=44)': f"{mnv_f} ({mnv_f/len(faricimab)*100:.1f})",
                'EYLEA (n=44)': f"{mnv_e} ({mnv_e/len(eylea)*100:.1f})",
                'P-value': format_pvalue(p_mnv)
            })
    
    # 基线BCVA
    if 'BCVA (BL)' in df.columns:
        bcva_f = faricimab['BCVA (BL)'].dropna()
        bcva_e = eylea['BCVA (BL)'].dropna()
        
        # 转换为ETDRS letters
        etdrs_f = convert_logmar_to_etdrs(bcva_f)
        etdrs_e = convert_logmar_to_etdrs(bcva_e)
        
        _, p_bcva = stats.ttest_ind(etdrs_f, etdrs_e)
        
        results.append({
            'Characteristic': 'Baseline BCVA, ETDRS letters (mean ± SD)',
            'FARICIMAB (n=44)': f"{etdrs_f.mean():.1f} ± {etdrs_f.std():.1f}",
            'EYLEA (n=44)': f"{etdrs_e.mean():.1f} ± {etdrs_e.std():.1f}",
            'P-value': format_pvalue(p_bcva)
        })
    
    # 系统性疾病
    systemic_diseases = ['hypertension', 'diabetes', 'cardiovascular', 'dyslipidemia']
    disease_names = ['Hypertension', 'Diabetes mellitus', 'Cardiovascular disease', 'Dyslipidemia']
    
    for disease, name in zip(systemic_diseases, disease_names):
        if disease in df.columns:
            disease_f = faricimab[disease].sum()
            disease_e = eylea[disease].sum()
            
            contingency = [[disease_f, len(faricimab) - disease_f],
                          [disease_e, len(eylea) - disease_e]]
            _, p_disease = stats.chi2_contingency(contingency)[:2]
            
            results.append({
                'Characteristic': f'{name}, n (%)',
                'FARICIMAB (n=44)': f"{disease_f} ({disease_f/len(faricimab)*100:.1f})",
                'EYLEA (n=44)': f"{disease_e} ({disease_e/len(eylea)*100:.1f})",
                'P-value': format_pvalue(p_disease)
            })
    
    # 基线形态学指标
    morphology_indicators = ['IRF (BL)', 'SRF (BL)', 'IRF or SRF (BL)', 'SHRM (BL)']
    morphology_names = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    
    for indicator, name in zip(morphology_indicators, morphology_names):
        if indicator in df.columns:
            morph_f = faricimab[indicator].sum()
            morph_e = eylea[indicator].sum()
            
            contingency = [[morph_f, len(faricimab) - morph_f],
                          [morph_e, len(eylea) - morph_e]]
            _, p_morph = stats.chi2_contingency(contingency)[:2]
            
            results.append({
                'Characteristic': f'Baseline {name}, n (%)',
                'FARICIMAB (n=44)': f"{morph_f} ({morph_f/len(faricimab)*100:.1f})",
                'EYLEA (n=44)': f"{morph_e} ({morph_e/len(eylea)*100:.1f})",
                'P-value': format_pvalue(p_morph)
            })
    
    # 保存结果
    df_result = pd.DataFrame(results)
    output_path = '../tables/PSM_Table1_Baseline_Characteristics.csv'
    df_result.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    print(f"✓ Table 1 已保存: {output_path}")
    return df_result

def generate_table2_visual_acuity_outcomes(df):
    """生成Table 2: 视力结果表"""
    print("\n📋 生成Table 2: 视力结果表...")

    # 分组数据
    faricimab = df[df['Drug'] == 'FARICIMAB']
    eylea = df[df['Drug'] == 'EYLEA']

    results = []

    # 时间点和对应的列名
    timepoints = [
        ('Baseline', 'BCVA (BL)'),
        ('Post-Loading Phase', 'BCVA (Post-LP)'),
        ('12 Months', 'BCVA (Year 1)')
    ]

    for timepoint, col_name in timepoints:
        if col_name in df.columns:
            # 获取有效数据
            bcva_f = faricimab[col_name].dropna()
            bcva_e = eylea[col_name].dropna()

            if len(bcva_f) > 0 and len(bcva_e) > 0:
                # 转换为ETDRS letters
                etdrs_f = convert_logmar_to_etdrs(bcva_f)
                etdrs_e = convert_logmar_to_etdrs(bcva_e)

                # 统计检验
                _, p_value = stats.ttest_ind(etdrs_f, etdrs_e)

                results.append({
                    'Timepoint': timepoint,
                    f'FARICIMAB (n={len(bcva_f)})': f"{etdrs_f.mean():.1f} ± {etdrs_f.std():.1f}",
                    f'EYLEA (n={len(bcva_e)})': f"{etdrs_e.mean():.1f} ± {etdrs_e.std():.1f}",
                    'P-value': format_pvalue(p_value)
                })

    # 计算改善量
    if 'BCVA (BL)' in df.columns and 'BCVA (Post-LP)' in df.columns:
        # Post-LP改善量
        f_bl = faricimab['BCVA (BL)'].dropna()
        f_postlp = faricimab['BCVA (Post-LP)'].dropna()
        e_bl = eylea['BCVA (BL)'].dropna()
        e_postlp = eylea['BCVA (Post-LP)'].dropna()

        # 找到同时有基线和Post-LP数据的患者
        f_both = faricimab.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)'])
        e_both = eylea.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)'])

        if len(f_both) > 0 and len(e_both) > 0:
            f_change = convert_logmar_to_etdrs(f_both['BCVA (BL)']) - convert_logmar_to_etdrs(f_both['BCVA (Post-LP)'])
            e_change = convert_logmar_to_etdrs(e_both['BCVA (BL)']) - convert_logmar_to_etdrs(e_both['BCVA (Post-LP)'])

            _, p_change = stats.ttest_ind(f_change, e_change)

            results.append({
                'Timepoint': 'Change from Baseline to Post-LP',
                f'FARICIMAB (n={len(f_both)})': f"{f_change.mean():.1f} ± {f_change.std():.1f}",
                f'EYLEA (n={len(e_both)})': f"{e_change.mean():.1f} ± {e_change.std():.1f}",
                'P-value': format_pvalue(p_change)
            })

    if 'BCVA (BL)' in df.columns and 'BCVA (Year 1)' in df.columns:
        # 12个月改善量
        f_both_y1 = faricimab.dropna(subset=['BCVA (BL)', 'BCVA (Year 1)'])
        e_both_y1 = eylea.dropna(subset=['BCVA (BL)', 'BCVA (Year 1)'])

        if len(f_both_y1) > 0 and len(e_both_y1) > 0:
            f_change_y1 = convert_logmar_to_etdrs(f_both_y1['BCVA (BL)']) - convert_logmar_to_etdrs(f_both_y1['BCVA (Year 1)'])
            e_change_y1 = convert_logmar_to_etdrs(e_both_y1['BCVA (BL)']) - convert_logmar_to_etdrs(e_both_y1['BCVA (Year 1)'])

            _, p_change_y1 = stats.ttest_ind(f_change_y1, e_change_y1)

            results.append({
                'Timepoint': 'Change from Baseline to 12 Months',
                f'FARICIMAB (n={len(f_both_y1)})': f"{f_change_y1.mean():.1f} ± {f_change_y1.std():.1f}",
                f'EYLEA (n={len(e_both_y1)})': f"{e_change_y1.mean():.1f} ± {e_change_y1.std():.1f}",
                'P-value': format_pvalue(p_change_y1)
            })

    # 保存结果
    df_result = pd.DataFrame(results)
    output_path = '../tables/PSM_Table2_Visual_Acuity_Outcomes.csv'
    df_result.to_csv(output_path, index=False, encoding='utf-8-sig')

    print(f"✓ Table 2 已保存: {output_path}")
    return df_result

def main():
    """主函数"""
    print("🚀 开始生成PSM匹配后分析表格...")
    
    # 创建输出目录
    os.makedirs('../tables', exist_ok=True)
    
    # 加载数据
    df = load_psm_data()
    
    # 生成Table 1
    table1 = generate_table1_baseline_characteristics(df)

    # 生成Table 2
    table2 = generate_table2_visual_acuity_outcomes(df)

    print("\n✅ PSM匹配后表格生成完成!")
    print("📁 输出文件位置:")
    print("  - Table 1: ../tables/PSM_Table1_Baseline_Characteristics.csv")
    print("  - Table 2: ../tables/PSM_Table2_Visual_Acuity_Outcomes.csv")

if __name__ == "__main__":
    main()
