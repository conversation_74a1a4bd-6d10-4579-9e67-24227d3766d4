# PSM分析文件夹说明

## 📁 文件夹内容概览

本文件夹包含了完整的倾向性评分匹配(PSM)分析流程，用于法瑞西单抗vs阿柏西普的nAMD治疗比较研究。

**文件夹已优化**: 删除了中间过程文件，仅保留核心必需文件。

## 📊 原始数据文件

### `nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx`
- **描述**: 原始研究数据
- **内容**: 172例患者的完整临床数据
- **分组**: FARICIMAB 86例, EYLEA 86例
- **随访**: FARICIMAB 44例有1年随访, EYLEA 82例有1年随访

## 🔧 PSM方法代码

### 1. `data_preprocessing_for_psm.py`
- **功能**: 数据预处理脚本
- **主要任务**:
  - 解析系统性疾病文本数据
  - 创建二元指示变量
  - 计算年龄
  - 筛选1年随访队列
  - 生成匹配变量汇总

### 2. `simple_psm_matching.py`
- **功能**: PSM匹配主程序
- **匹配策略**:
  - 基线BCVA权重: 60% (最重要)
  - 年龄权重: 15%
  - 性别权重: 10%
  - MNV分型权重: 5%×3
  - 排除高血压和糖尿病匹配
- **匹配方法**: 1:1最近邻匹配
- **距离计算**: 加权欧氏距离

### 3. `baseline_stats.py`
- **功能**: 基线特征统计脚本
- **输出**: 匹配前后的基线特征比较

## 🎯 PSM匹配结果 (核心文件)

### `PSM_Matched_Final_Dataset.csv`
- **描述**: 最终匹配数据集
- **样本量**: 88例 (44对患者)
- **用途**: 后续所有分析的基础数据
- **注意**: 包含完整的原始临床变量，已删除PSM过程中的辅助变量

### `PSM_Balance_Assessment_Final.csv`
- **描述**: 平衡性评估结果
- **内容**: 匹配前后SMD和P值比较
- **关键指标**: 基线BCVA SMD = 0.008 (优秀)

## 🎯 PSM匹配结果总结

### ✅ 匹配成功指标:
- **样本量**: 44对患者 (达到目标)
- **基线BCVA**: 完美匹配 (SMD=0.008, P=0.969)
- **年龄**: 良好匹配 (差异0.2岁)
- **IRF**: 完全匹配 (50% vs 50%)

### 📊 关键基线特征:
- **年龄**: FARICIMAB 80.9±5.9岁 vs EYLEA 81.1±6.8岁
- **女性**: FARICIMAB 52.3% vs EYLEA 61.4%
- **基线BCVA**: FARICIMAB 55.8±19.2 vs EYLEA 55.6±20.3 letters
- **MNV Type1**: FARICIMAB 63.6% vs EYLEA 52.3%

### ⚠️ 未匹配变量:
- 高血压、糖尿病、心血管疾病、血脂异常保持原始分布
- 这些差异反映了真实世界的患者特征

## 🔄 使用说明

1. **运行预处理**: `python data_preprocessing_for_psm.py`
2. **执行PSM匹配**: `python simple_psm_matching.py`
3. **查看基线统计**: `python baseline_stats.py`
4. **后续分析**: 使用 `PSM_Matched_Final_Dataset.csv` 作为数据源

## � 文件夹优化说明

**已删除的中间文件** (可通过代码重新生成):
- `Preprocessed_Data_for_PSM.csv` - 预处理数据
- `FARICIMAB_1Year_Cohort.csv` - 单组数据
- `EYLEA_1Year_Cohort.csv` - 单组数据
- `PSM_Matching_Variables.csv` - 变量汇总
- `PSM_Matched_Pairs.csv` - 匹配对信息
- `Detailed_Systemic_Diseases_Analysis.csv` - 探索性分析

**保留的核心文件**:
- 原始数据 (Excel文件)
- PSM方法代码 (3个Python文件)
- 最终匹配结果 (PSM_Matched_Final_Dataset.csv)
- 平衡性评估 (PSM_Balance_Assessment_Final.csv)
- 说明文档 (README_PSM_Analysis.md)

## �📝 方法学说明

本PSM分析采用了以下策略:
- 优先匹配临床最重要的预后因素(基线BCVA)
- 使用贪心算法确保所有44例FARICIMAB患者都能找到匹配
- 排除了数据质量存在问题的系统性疾病变量
- 保持了足够的样本量进行有效的统计分析

---
**创建时间**: 2025年1月
**最后更新**: 2025年1月 (文件夹优化)
**分析目的**: 法瑞西单抗vs阿柏西普nAMD治疗效果比较
**匹配质量**: 优秀 (基线BCVA SMD<0.01)
**文件状态**: 已优化，仅保留核心必需文件
