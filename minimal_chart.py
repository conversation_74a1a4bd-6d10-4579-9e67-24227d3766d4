#!/usr/bin/env python3
import sys
import os

# 添加调试信息
print("=== 开始执行 ===")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")

try:
    import pandas as pd
    print("✅ pandas导入成功")
except Exception as e:
    print(f"❌ pandas导入失败: {e}")
    sys.exit(1)

try:
    import matplotlib
    matplotlib.use('Agg')  # 非交互式后端
    import matplotlib.pyplot as plt
    print("✅ matplotlib导入成功")
except Exception as e:
    print(f"❌ matplotlib导入失败: {e}")
    sys.exit(1)

try:
    # 读取PSM数据
    print("📂 读取PSM数据...")
    df = pd.read_csv('PSM_Analysis/PSM_Matched_Final_Dataset.csv')
    print(f"✅ 数据读取成功: {len(df)}行")
    
    # 检查关键列
    print("检查关键列:")
    for col in ['Drug', 'BCVA (BL)', 'BCVA (Year 1)']:
        if col in df.columns:
            print(f"  ✅ {col}")
        else:
            print(f"  ❌ {col}")
    
    # 分组数据
    faricimab = df[df['Drug'] == 'FARICIMAB']
    eylea = df[df['Drug'] == 'EYLEA']
    print(f"FARICIMAB: {len(faricimab)}例")
    print(f"EYLEA: {len(eylea)}例")
    
    # 转换BCVA数据
    f_bcva_bl = 85 - 50 * pd.to_numeric(faricimab['BCVA (BL)'], errors='coerce')
    e_bcva_bl = 85 - 50 * pd.to_numeric(eylea['BCVA (BL)'], errors='coerce')
    
    f_mean = f_bcva_bl.mean()
    e_mean = e_bcva_bl.mean()
    
    print(f"FARICIMAB基线BCVA: {f_mean:.1f}")
    print(f"EYLEA基线BCVA: {e_mean:.1f}")
    
    # 创建简单图表
    print("📊 创建图表...")
    plt.figure(figsize=(8, 6))
    
    groups = ['FARICIMAB', 'EYLEA']
    values = [f_mean, e_mean]
    colors = ['#A23B72', '#2E86AB']
    
    bars = plt.bar(groups, values, color=colors, alpha=0.8)
    
    # 添加数值标注
    for bar, value in zip(bars, values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value:.1f}', ha='center', va='bottom', fontsize=12)
    
    plt.title('PSM Post-Matching BCVA Baseline Comparison', fontsize=14, fontweight='bold')
    plt.ylabel('BCVA (ETDRS letters)', fontsize=12)
    plt.ylim(0, 80)
    plt.grid(True, alpha=0.3)
    
    # 保存图表
    output_file = 'PSM_Minimal_Chart.pdf'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 图表保存成功: {output_file}")
    
    # 验证文件是否存在
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"✅ 文件确认存在，大小: {file_size} bytes")
    else:
        print("❌ 文件未找到")
    
    print("=== 执行完成 ===")

except Exception as e:
    print(f"❌ 执行失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
