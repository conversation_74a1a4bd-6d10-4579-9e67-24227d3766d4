#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSM后图表生成 - 在主目录运行
基于成功的generate_new_charts.py修改
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

# 颜色配置
COLORS = {
    'EYLEA': '#2E86AB',      # 蓝色
    'FARICIMAB': '#A23B72',  # 紫红色
}

def load_psm_data():
    """加载PSM数据并返回两个数据集"""
    print("📂 加载PSM数据...")
    
    # 读取PSM CSV文件
    file_path = 'PSM_Analysis/PSM_Matched_Final_Dataset.csv'
    df = pd.read_csv(file_path)
    
    print(f"✓ PSM数据加载完成，共 {len(df)} 条记录")
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'
        
        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    # 分层策略：
    # 1. 完整队列（用于基线和Post-LP分析）
    df_postlp = df.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)']).copy()
    
    # 2. 长期随访队列（用于1年分析）
    df_year1 = df.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']).copy()
    
    print(f"✓ 完整队列（基线+Post-LP）: {len(df_postlp)} 条记录")
    print(f"✓ 长期随访队列（1年）: {len(df_year1)} 条记录")
    
    return df_postlp, df_year1

def convert_logmar_to_letters(logmar_values):
    """将logMAR转换为ETDRS letters"""
    return 85 - 50 * logmar_values

def create_bcva_four_panel_analysis(df_postlp, df_year1):
    """生成BCVA四图分析：整体 + MNV1 + MNV2 + MNV3（仅ETDRS letters）"""
    print("\n📊 生成PSM后BCVA四图分析（仅ETDRS letters）...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    timepoints = ['Baseline', 'Post-LP', '12 months']
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    
    # 分析组别：整体 + MNV1-3
    analysis_groups = [
        ('Overall', None),
        ('MNV Type 1', 1),
        ('MNV Type 2', 2),
        ('MNV Type 3', 3)
    ]
    
    for idx, (group_name, mnv_type) in enumerate(analysis_groups):
        ax = axes[idx]
        
        # 选择数据集
        if mnv_type is None:
            data = df_year1
        else:
            data = df_year1[df_year1['MNV Type'] == mnv_type]
        
        if len(data) == 0:
            ax.text(0.5, 0.5, 'No data available', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'{group_name} (n=0)')
            continue
        
        # 分组数据
        faricimab_data = data[data['Drug'] == 'FARICIMAB']
        eylea_data = data[data['Drug'] == 'EYLEA']
        
        # 计算ETDRS letters均值和标准误
        faricimab_means = []
        faricimab_sems = []
        eylea_means = []
        eylea_sems = []
        
        for col in bcva_cols:
            if col in data.columns:
                # 转换为ETDRS letters
                f_letters = convert_logmar_to_letters(pd.to_numeric(faricimab_data[col], errors='coerce')).dropna()
                e_letters = convert_logmar_to_letters(pd.to_numeric(eylea_data[col], errors='coerce')).dropna()
                
                faricimab_means.append(f_letters.mean() if len(f_letters) > 0 else 0)
                faricimab_sems.append(f_letters.sem() if len(f_letters) > 0 else 0)
                eylea_means.append(e_letters.mean() if len(e_letters) > 0 else 0)
                eylea_sems.append(e_letters.sem() if len(e_letters) > 0 else 0)
            else:
                faricimab_means.append(0)
                faricimab_sems.append(0)
                eylea_means.append(0)
                eylea_sems.append(0)
        
        # 绘制柱状图
        x = np.arange(len(timepoints))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, faricimab_means, width, yerr=faricimab_sems, capsize=5,
                      label=f'FARICIMAB (n={len(faricimab_data)})', color=COLORS['FARICIMAB'], alpha=0.8)
        bars2 = ax.bar(x + width/2, eylea_means, width, yerr=eylea_sems, capsize=5,
                      label=f'EYLEA (n={len(eylea_data)})', color=COLORS['EYLEA'], alpha=0.8)
        
        # 添加数值标注
        for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
            height1 = bar1.get_height()
            height2 = bar2.get_height()
            ax.text(bar1.get_x() + bar1.get_width()/2., height1 + 1,
                   f'{height1:.1f}', ha='center', va='bottom', fontsize=10)
            ax.text(bar2.get_x() + bar2.get_width()/2., height2 + 1,
                   f'{height2:.1f}', ha='center', va='bottom', fontsize=10)
        
        # 设置图表
        ax.set_xlabel('Time Point')
        ax.set_ylabel('BCVA (ETDRS letters)')
        ax.set_title(f'{group_name} (n={len(data)})')
        ax.set_xticks(x)
        ax.set_xticklabels(timepoints)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 85)
    
    plt.suptitle('PSM Post-Matching BCVA Analysis: Four-Panel Comparison', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('PSM_BCVA_Four_Panel_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ PSM后BCVA四图分析生成完成: PSM_BCVA_Four_Panel_Analysis.pdf")

def create_morphology_heatmap(df_postlp):
    """生成形态学热图"""
    print("\n📊 生成PSM后形态学热图...")
    
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # 形态学指标
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    # 为每个药物组创建热图
    for drug_idx, drug in enumerate(['FARICIMAB', 'EYLEA']):
        drug_data = df_postlp[df_postlp['Drug'] == drug]
        
        # 计算阳性率矩阵
        heatmap_data = []
        
        for indicator in morphology_indicators:
            row_data = []
            for timepoint in timepoints:
                col_name = f'{indicator} ({timepoint})'
                if col_name in drug_data.columns:
                    positive_rate = (drug_data[col_name] == 1).mean() * 100
                else:
                    positive_rate = 0
                row_data.append(positive_rate)
            heatmap_data.append(row_data)
        
        # 绘制热图
        ax = axes[drug_idx]
        im = ax.imshow(heatmap_data, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=100)
        
        # 添加数值标注
        for i in range(len(morphology_indicators)):
            for j in range(len(timepoints)):
                text = f'{heatmap_data[i][j]:.1f}%'
                ax.text(j, i, text, ha='center', va='center', fontsize=12, fontweight='bold')
        
        ax.set_xticks(range(len(timepoints)))
        ax.set_xticklabels(timepoints)
        ax.set_yticks(range(len(morphology_indicators)))
        ax.set_yticklabels(morphology_indicators)
        ax.set_title(f'{drug} (n={len(drug_data)})')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Positive Rate (%)')
    
    plt.suptitle('PSM Post-Matching Morphological Features Heatmap', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('PSM_Morphology_Heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ PSM后形态学热图生成完成: PSM_Morphology_Heatmap.pdf")

def main():
    """主函数"""
    print("🚀 开始生成PSM后图表...")
    print("=" * 50)
    
    try:
        # 加载数据
        df_postlp, df_year1 = load_psm_data()
        
        # 生成BCVA四图分析
        create_bcva_four_panel_analysis(df_postlp, df_year1)
        
        # 生成形态学热图
        create_morphology_heatmap(df_postlp)
        
        print("\n" + "=" * 50)
        print("✅ PSM后图表生成完成!")
        print("📁 生成的文件:")
        print("  - PSM_BCVA_Four_Panel_Analysis.pdf")
        print("  - PSM_Morphology_Heatmap.pdf")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
