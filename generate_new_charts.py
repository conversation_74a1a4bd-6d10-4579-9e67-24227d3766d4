#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新图表生成脚本 - 按用户需求重新设计
1. BCVA四图分析（仅ETDRS letters）：整体 + MNV1-3
2. 病灶形态学四图分析（柱状图+折线图）：4个四图图分别对应整体、MNV1-3
3. 形态学热图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

# 颜色配置
COLORS = {
    'EYLEA': '#2E86AB',      # 蓝色
    'FARICIMAB': '#A23B72',  # 紫红色
}

def load_data():
    """加载数据并返回两个数据集"""
    print("📂 加载数据...")
    
    # 读取Excel文件
    file_path = 'nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx'
    df = pd.read_excel(file_path)
    
    print(f"✓ 原始数据加载完成，共 {len(df)} 条记录")
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'
        
        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    # 分层策略：
    # 1. 完整队列（用于基线和Post-LP分析）
    df_postlp = df.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)']).copy()
    
    # 2. 长期随访队列（用于1年分析）
    df_year1 = df.dropna(subset=['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']).copy()
    
    print(f"✓ 完整队列（基线+Post-LP）: {len(df_postlp)} 条记录")
    print(f"✓ 长期随访队列（1年）: {len(df_year1)} 条记录")
    
    return df_postlp, df_year1

def convert_logmar_to_letters(logmar_values):
    """将logMAR转换为ETDRS letters"""
    return 85 - 50 * logmar_values

def create_bcva_four_panel_analysis(df_postlp, df_year1):
    """生成BCVA四图分析：整体 + MNV1 + MNV2 + MNV3（仅ETDRS letters）"""
    print("\n📊 生成BCVA四图分析（仅ETDRS letters）...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    timepoints = ['Baseline', 'Post-LP', '12 months']
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    
    # 分析组别：整体 + MNV1-3
    analysis_groups = [
        ('Overall', None),
        ('MNV Type 1', 1),
        ('MNV Type 2', 2),
        ('MNV Type 3', 3)
    ]
    
    for idx, (group_name, mnv_type) in enumerate(analysis_groups):
        ax = axes[idx]
        
        # 筛选数据
        if mnv_type is None:
            # 整体数据
            current_postlp = df_postlp
            current_year1 = df_year1
        else:
            # 特定MNV类型
            current_postlp = df_postlp[df_postlp['MNV Type'] == mnv_type]
            current_year1 = df_year1[df_year1['MNV Type'] == mnv_type]
        
        eylea_letters_means = []
        faricimab_letters_means = []
        eylea_letters_sems = []
        faricimab_letters_sems = []
        eylea_ns = []
        faricimab_ns = []
        
        # 基线和Post-LP使用完整队列
        for i, col in enumerate(bcva_cols[:2]):
            eylea_values = current_postlp[current_postlp['Drug'] == 'EYLEA'][col].dropna()
            faricimab_values = current_postlp[current_postlp['Drug'] == 'FARICIMAB'][col].dropna()
            
            if len(eylea_values) > 0 and len(faricimab_values) > 0:
                eylea_letters = convert_logmar_to_letters(eylea_values)
                faricimab_letters = convert_logmar_to_letters(faricimab_values)
                
                eylea_letters_means.append(eylea_letters.mean())
                faricimab_letters_means.append(faricimab_letters.mean())
                eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
                faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))
                eylea_ns.append(len(eylea_values))
                faricimab_ns.append(len(faricimab_values))
            else:
                eylea_letters_means.append(np.nan)
                faricimab_letters_means.append(np.nan)
                eylea_letters_sems.append(np.nan)
                faricimab_letters_sems.append(np.nan)
                eylea_ns.append(0)
                faricimab_ns.append(0)

        # 1年使用长期随访队列
        eylea_values = current_year1[current_year1['Drug'] == 'EYLEA']['BCVA (Year 1)'].dropna()
        faricimab_values = current_year1[current_year1['Drug'] == 'FARICIMAB']['BCVA (Year 1)'].dropna()

        if len(eylea_values) > 0 and len(faricimab_values) > 0:
            eylea_letters = convert_logmar_to_letters(eylea_values)
            faricimab_letters = convert_logmar_to_letters(faricimab_values)
            
            eylea_letters_means.append(eylea_letters.mean())
            faricimab_letters_means.append(faricimab_letters.mean())
            eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
            faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))
            eylea_ns.append(len(eylea_values))
            faricimab_ns.append(len(faricimab_values))
        else:
            eylea_letters_means.append(np.nan)
            faricimab_letters_means.append(np.nan)
            eylea_letters_sems.append(np.nan)
            faricimab_letters_sems.append(np.nan)
            eylea_ns.append(0)
            faricimab_ns.append(0)

        x_pos = np.arange(len(timepoints))

        # 绘制线条 - 区分完整队列和长期随访队列
        # BL到Post-LP：实线（完整队列）
        if not np.isnan(eylea_letters_means[0]) and not np.isnan(eylea_letters_means[1]):
            ax.errorbar(x_pos[:2], eylea_letters_means[:2], yerr=eylea_letters_sems[:2],
                       marker='o', linewidth=3, markersize=8, capsize=5,
                       color=COLORS['EYLEA'], alpha=0.8)
            ax.errorbar(x_pos[:2], faricimab_letters_means[:2], yerr=faricimab_letters_sems[:2],
                       marker='s', linewidth=3, markersize=8, capsize=5,
                       color=COLORS['FARICIMAB'], alpha=0.8)

        # Post-LP到Year 1：虚线（长期随访队列）
        if not np.isnan(eylea_letters_means[1]) and not np.isnan(eylea_letters_means[2]):
            ax.errorbar(x_pos[1:], eylea_letters_means[1:], yerr=eylea_letters_sems[1:],
                       marker='o', linewidth=2, markersize=8, capsize=5, linestyle='--',
                       color=COLORS['EYLEA'], alpha=0.6)
            ax.errorbar(x_pos[1:], faricimab_letters_means[1:], yerr=faricimab_letters_sems[1:],
                       marker='s', linewidth=2, markersize=8, capsize=5, linestyle='--',
                       color=COLORS['FARICIMAB'], alpha=0.6)

        # 添加数值标签
        for i, (eylea_mean, faricimab_mean) in enumerate(zip(eylea_letters_means, faricimab_letters_means)):
            if not np.isnan(eylea_mean) and not np.isnan(faricimab_mean):
                # EYLEA数值标签
                ax.text(i, eylea_mean + eylea_letters_sems[i] + 2, f'{eylea_mean:.1f}', 
                       ha='center', va='bottom', fontsize=9, color=COLORS['EYLEA'], fontweight='bold')
                # FARICIMAB数值标签
                ax.text(i, faricimab_mean - faricimab_letters_sems[i] - 2, f'{faricimab_mean:.1f}', 
                       ha='center', va='top', fontsize=9, color=COLORS['FARICIMAB'], fontweight='bold')
        
        # 添加样本量标注
        for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
            if e_n > 0 and f_n > 0 and not np.isnan(eylea_letters_means[i]):
                y_max = max(eylea_letters_means[i] + eylea_letters_sems[i],
                           faricimab_letters_means[i] + faricimab_letters_sems[i])
                ax.text(i, y_max + 8, f'n={e_n}/{f_n}', ha='center', va='bottom', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

        ax.set_title(f'{group_name}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('BCVA (ETDRS Letters)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(timepoints)
        
        # 只在第一个子图添加图例
        if idx == 0:
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], color=COLORS['EYLEA'], marker='o', linewidth=2, label='EYLEA'),
                Line2D([0], [0], color=COLORS['FARICIMAB'], marker='s', linewidth=2, label='FARICIMAB'),
                Line2D([0], [0], color='gray', linewidth=3, label='Complete cohort'),
                Line2D([0], [0], color='gray', linewidth=2, linestyle='--', label='Long-term cohort')
            ]
            ax.legend(handles=legend_elements, fontsize=9, loc='upper left')

        ax.grid(True, alpha=0.3)
        ax.set_ylim(40, 80)  # 统一Y轴范围

    plt.suptitle('BCVA Changes Analysis (ETDRS Letters)', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('PSM_BCVA_Four_Panel_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ BCVA四图分析已保存: New_BCVA_Four_Panel_Analysis.pdf")

def create_morphology_four_panel_analysis(df_postlp, df_year1, group_name, mnv_type=None):
    """生成单个组别的病灶形态学四图分析（柱状图+折线图）"""
    print(f"\n📊 生成{group_name}病灶形态学四图分析...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()

    # 形态学指标
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '12 months']

    # 筛选数据
    if mnv_type is None:
        # 整体数据
        current_postlp = df_postlp
        current_year1 = df_year1
    else:
        # 特定MNV类型
        current_postlp = df_postlp[df_postlp['MNV Type'] == mnv_type]
        current_year1 = df_year1[df_year1['MNV Type'] == mnv_type]

    for idx, indicator in enumerate(morphology_indicators):
        ax = axes[idx]

        eylea_rates = []
        faricimab_rates = []
        eylea_ns = []
        faricimab_ns = []

        for i, timepoint in enumerate(timepoints):
            col_name = f'{indicator} ({timepoint})'

            # 根据时间点选择数据集
            if timepoint == 'Year 1':
                current_df = current_year1
            else:
                current_df = current_postlp

            if col_name in current_df.columns:
                eylea_data = current_df[current_df['Drug'] == 'EYLEA']
                faricimab_data = current_df[current_df['Drug'] == 'FARICIMAB']

                eylea_values = eylea_data[col_name].dropna()
                faricimab_values = faricimab_data[col_name].dropna()

                if len(eylea_values) > 0 and len(faricimab_values) > 0:
                    eylea_rate = (eylea_values == 1).mean() * 100
                    faricimab_rate = (faricimab_values == 1).mean() * 100

                    eylea_rates.append(eylea_rate)
                    faricimab_rates.append(faricimab_rate)
                    eylea_ns.append(len(eylea_values))
                    faricimab_ns.append(len(faricimab_values))
                else:
                    eylea_rates.append(0)
                    faricimab_rates.append(0)
                    eylea_ns.append(0)
                    faricimab_ns.append(0)

        x_pos = np.arange(len(time_labels))
        width = 0.35

        # 绘制柱状图
        bars1 = ax.bar(x_pos - width/2, eylea_rates, width,
                      color=COLORS['EYLEA'], alpha=0.7, label='EYLEA')
        bars2 = ax.bar(x_pos + width/2, faricimab_rates, width,
                      color=COLORS['FARICIMAB'], alpha=0.7, label='FARICIMAB')

        # 绘制折线图（叠加在柱状图上）
        ax.plot(x_pos - width/2, eylea_rates, marker='o', linewidth=2, markersize=6,
               color=COLORS['EYLEA'], alpha=0.9)
        ax.plot(x_pos + width/2, faricimab_rates, marker='s', linewidth=2, markersize=6,
               color=COLORS['FARICIMAB'], alpha=0.9)

        # 添加百分比标签
        for i, (eylea_rate, faricimab_rate) in enumerate(zip(eylea_rates, faricimab_rates)):
            if eylea_ns[i] > 0 and faricimab_ns[i] > 0:
                # EYLEA百分比标签
                ax.text(i - width/2, eylea_rate + 2, f'{eylea_rate:.1f}%',
                       ha='center', va='bottom', fontsize=9, color=COLORS['EYLEA'], fontweight='bold')
                # FARICIMAB百分比标签
                ax.text(i + width/2, faricimab_rate + 2, f'{faricimab_rate:.1f}%',
                       ha='center', va='bottom', fontsize=9, color=COLORS['FARICIMAB'], fontweight='bold')

        # 添加样本量标注
        for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
            if e_n > 0 and f_n > 0:
                y_max = max(eylea_rates[i], faricimab_rates[i])
                ax.text(i, y_max + 15, f'n={e_n}/{f_n}', ha='center', va='bottom', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

        ax.set_title(f'{indicator}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('Prevalence (%)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(time_labels)

        # 只在第一个子图添加图例
        if idx == 0:
            ax.legend(fontsize=10, loc='upper right')

        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 120)  # 增加Y轴上限，为超过100%的数据和标签留出空间

    plt.suptitle(f'Morphological Features Analysis - {group_name}', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # 保存文件
    filename = f'PSM_Morphology_{group_name.replace(" ", "_")}_Analysis.pdf'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✓ {group_name}病灶形态学四图分析已保存: {filename}")

def create_morphology_heatmap(df_postlp, df_year1):
    """生成形态学热图 - 与Updated_Figure3_Morphology_Heatmap.pdf保持一致"""
    print("\n📊 生成形态学热图（与现有热图保持一致）...")

    # 形态学指标 (按新顺序: IRF, SRF, IRF or SRF, SHRM)
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']

    # 创建数据矩阵和样本量矩阵
    eylea_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    faricimab_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    eylea_n_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    faricimab_n_matrix = np.zeros((len(morphology_indicators), len(timepoints)))

    for i, indicator in enumerate(morphology_indicators):
        for j, timepoint in enumerate(timepoints):
            col_name = f'{indicator} ({timepoint})'

            # 根据时间点选择数据集
            if timepoint == 'Year 1':
                current_df = df_year1
            else:
                current_df = df_postlp

            if col_name in current_df.columns:
                eylea_data = current_df[current_df['Drug'] == 'EYLEA']
                faricimab_data = current_df[current_df['Drug'] == 'FARICIMAB']

                # 计算有效数据
                eylea_valid = eylea_data[col_name].dropna()
                faricimab_valid = faricimab_data[col_name].dropna()

                # 计算患病率
                eylea_rate = (eylea_valid == 1).mean() * 100 if len(eylea_valid) > 0 else 0
                faricimab_rate = (faricimab_valid == 1).mean() * 100 if len(faricimab_valid) > 0 else 0

                eylea_matrix[i, j] = eylea_rate
                faricimab_matrix[i, j] = faricimab_rate
                eylea_n_matrix[i, j] = len(eylea_valid)
                faricimab_n_matrix[i, j] = len(faricimab_valid)

    # 创建图表 - 2x2布局
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))

    # EYLEA热图
    sns.heatmap(eylea_matrix, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax1, cbar_kws={'label': 'Prevalence (%)'}, vmin=0, vmax=100)
    ax1.set_title('A. EYLEA Group - Prevalence (%)', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # FARICIMAB热图
    sns.heatmap(faricimab_matrix, annot=True, fmt='.1f', cmap='Reds',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax2, cbar_kws={'label': 'Prevalence (%)'}, vmin=0, vmax=100)
    ax2.set_title('B. FARICIMAB Group - Prevalence (%)', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # EYLEA样本量热图
    sns.heatmap(eylea_n_matrix, annot=True, fmt='.0f', cmap='Greens',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax3, cbar_kws={'label': 'Sample Size (n)'})
    ax3.set_title('C. EYLEA Group - Sample Size (n)', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # FARICIMAB样本量热图
    sns.heatmap(faricimab_n_matrix, annot=True, fmt='.0f', cmap='Oranges',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax4, cbar_kws={'label': 'Sample Size (n)'})
    ax4.set_title('D. FARICIMAB Group - Sample Size (n)', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # 添加说明
    fig.text(0.5, 0.02, 'BL & Post-LP: Complete cohort; Year 1: Long-term follow-up cohort',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.08)
    plt.savefig('PSM_Morphology_Heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ 形态学热图已保存: New_Morphology_Heatmap.pdf")

if __name__ == "__main__":
    print("🚀 开始生成新版图表...")

    try:
        # 1. 加载数据
        df_postlp, df_year1 = load_data()

        # 2. 生成BCVA四图分析（1个PDF）
        create_bcva_four_panel_analysis(df_postlp, df_year1)

        # 3. 生成病灶形态学四图分析（4个PDF）
        analysis_groups = [
            ('Overall', None),
            ('MNV_Type_1', 1),
            ('MNV_Type_2', 2),
            ('MNV_Type_3', 3)
        ]

        for group_name, mnv_type in analysis_groups:
            create_morphology_four_panel_analysis(df_postlp, df_year1, group_name, mnv_type)

        # 4. 生成形态学热图（1个PDF）
        create_morphology_heatmap(df_postlp, df_year1)

        print("\n================================================================================")
        print("✅ 所有新版图表生成完成！")
        print("================================================================================")
        print("\n📁 生成的图表文件列表:")
        print("✓ 1. New_BCVA_Four_Panel_Analysis.pdf - BCVA四图分析（整体+MNV1-3）")
        print("✓ 2. New_Morphology_Overall_Analysis.pdf - 整体病灶形态学四图分析")
        print("✓ 3. New_Morphology_MNV_Type_1_Analysis.pdf - MNV1病灶形态学四图分析")
        print("✓ 4. New_Morphology_MNV_Type_2_Analysis.pdf - MNV2病灶形态学四图分析")
        print("✓ 5. New_Morphology_MNV_Type_3_Analysis.pdf - MNV3病灶形态学四图分析")
        print("✓ 6. New_Morphology_Heatmap.pdf - 形态学热图")
        print("\n🎯 设计特点:")
        print("- ✅ BCVA图表：仅ETDRS letters，有误差线，标注样本量")
        print("- ✅ 病灶图表：柱状图+折线图组合，无误差线，标注百分比")
        print("- ✅ 统一格式：每个四图图内容一致，便于比较")
        print("- ✅ 科学准确：符合统计学原理和医学期刊标准")
        print("================================================================================")

    except Exception as e:
        print(f"\n❌ 生成图表时出错: {str(e)}")
        import traceback
        traceback.print_exc()
