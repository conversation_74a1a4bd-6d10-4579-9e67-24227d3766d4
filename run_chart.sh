#!/bin/bash

echo "开始执行PSM图表生成..."
echo "当前目录: $(pwd)"
echo "Python版本: $(python3 --version 2>&1)"

# 设置环境变量
export MPLBACKEND=Agg

# 创建简单的Python脚本并执行
cat > temp_chart.py << 'EOF'
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

print("开始生成PSM图表...")

# 读取数据
df = pd.read_csv('PSM_Analysis/PSM_Matched_Final_Dataset.csv')
print(f"数据读取成功: {len(df)}行")

# 分组
faricimab = df[df['Drug'] == 'FARICIMAB']
eylea = df[df['Drug'] == 'EYLEA']

# 转换BCVA
f_bl = 85 - 50 * pd.to_numeric(faricimab['BCVA (BL)'], errors='coerce')
e_bl = 85 - 50 * pd.to_numeric(eylea['BCVA (BL)'], errors='coerce')

# 创建图表
plt.figure(figsize=(8, 6))
groups = ['FARICIMAB', 'EYLEA']
values = [f_bl.mean(), e_bl.mean()]
colors = ['#A23B72', '#2E86AB']

bars = plt.bar(groups, values, color=colors, alpha=0.8)

for bar, value in zip(bars, values):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{value:.1f}', ha='center', va='bottom')

plt.title('PSM Post-Matching BCVA Baseline')
plt.ylabel('BCVA (ETDRS letters)')
plt.ylim(0, 80)
plt.grid(True, alpha=0.3)

plt.savefig('PSM_Chart_Generated.pdf', dpi=300, bbox_inches='tight')
plt.close()

print("图表生成完成: PSM_Chart_Generated.pdf")

# 验证文件
import os
if os.path.exists('PSM_Chart_Generated.pdf'):
    size = os.path.getsize('PSM_Chart_Generated.pdf')
    print(f"文件确认存在，大小: {size} bytes")
else:
    print("文件未找到")
EOF

# 执行Python脚本
echo "执行Python脚本..."
python3 temp_chart.py

# 检查结果
if [ -f "PSM_Chart_Generated.pdf" ]; then
    echo "✅ 成功生成: PSM_Chart_Generated.pdf"
    ls -la PSM_Chart_Generated.pdf
else
    echo "❌ 文件生成失败"
fi

# 清理临时文件
rm -f temp_chart.py

echo "脚本执行完成"
