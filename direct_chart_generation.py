#!/usr/bin/env python3
"""
直接图表生成 - 最简化版本
"""

# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import os

def main():
    print("开始PSM图表生成...")
    
    # 读取数据
    df = pd.read_csv('PSM_Analysis/PSM_Matched_Final_Dataset.csv')
    print(f"数据加载: {len(df)}行")
    
    # 分组
    faricimab = df[df['Drug'] == 'FARICIMAB']
    eylea = df[df['Drug'] == 'EYLEA']
    
    # 转换BCVA
    f_bl = 85 - 50 * pd.to_numeric(faricimab['BCVA (BL)'], errors='coerce')
    e_bl = 85 - 50 * pd.to_numeric(eylea['BCVA (BL)'], errors='coerce')
    f_y1 = 85 - 50 * pd.to_numeric(faricimab['BCVA (Year 1)'], errors='coerce')
    e_y1 = 85 - 50 * pd.to_numeric(eylea['BCVA (Year 1)'], errors='coerce')
    
    # 创建BCVA对比图
    fig, ax = plt.subplots(figsize=(10, 6))
    
    x = np.arange(2)
    width = 0.35
    
    f_means = [f_bl.mean(), f_y1.mean()]
    e_means = [e_bl.mean(), e_y1.mean()]
    
    bars1 = ax.bar(x - width/2, f_means, width, label='FARICIMAB', color='#A23B72', alpha=0.8)
    bars2 = ax.bar(x + width/2, e_means, width, label='EYLEA', color='#2E86AB', alpha=0.8)
    
    # 添加数值
    for bar, value in zip(bars1, f_means):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
               f'{value:.1f}', ha='center', va='bottom')
    
    for bar, value in zip(bars2, e_means):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
               f'{value:.1f}', ha='center', va='bottom')
    
    ax.set_xlabel('Time Point')
    ax.set_ylabel('BCVA (ETDRS letters)')
    ax.set_title('PSM Post-Matching BCVA Comparison')
    ax.set_xticks(x)
    ax.set_xticklabels(['Baseline', '12 months'])
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 80)
    
    plt.tight_layout()
    plt.savefig('PSM_BCVA_Comparison.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("BCVA图表已生成: PSM_BCVA_Comparison.pdf")
    
    # 创建形态学图表
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('PSM Post-Matching Morphology Analysis')
    
    indicators = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)', 'IRF or SRF (BL)']
    
    for idx, indicator in enumerate(indicators):
        if idx >= 4:
            break
        ax = axes[idx // 2, idx % 2]
        
        if indicator in df.columns:
            f_rate = (faricimab[indicator] == 1).mean() * 100
            e_rate = (eylea[indicator] == 1).mean() * 100
            
            bars = ax.bar(['FARICIMAB', 'EYLEA'], [f_rate, e_rate], 
                         color=['#A23B72', '#2E86AB'], alpha=0.8)
            
            for bar, rate in zip(bars, [f_rate, e_rate]):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{rate:.1f}%', ha='center', va='bottom')
            
            ax.set_ylabel('Positive Rate (%)')
            ax.set_title(indicator.replace(' (BL)', ''))
            ax.set_ylim(0, 100)
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'Data not available', ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('PSM_Morphology_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("形态学图表已生成: PSM_Morphology_Analysis.pdf")
    
    # 验证文件
    files = ['PSM_BCVA_Comparison.pdf', 'PSM_Morphology_Analysis.pdf']
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} - {size} bytes")
        else:
            print(f"❌ {file} - 未找到")
    
    print("图表生成完成!")

if __name__ == "__main__":
    main()
